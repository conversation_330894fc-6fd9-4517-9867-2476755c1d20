# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import requests
from odoo.addons.namtn_tiktok.utils.tiktok_api_impl import TiktokApiImpl
from odoo.addons.namtn_tiktok.tools.sign import gen_sign
import json
from datetime import datetime
from ..tools.tiktok_tool import convert_str_date_to_timestamp


_logger = logging.getLogger(__name__)


class TiktokConfig(models.Model):
    _name = 'tiktok.config'
    _description = 'TiktokConfig'

    name = fields.Char('Name')
    base_url = fields.Char('Base URL')
    auth_url = fields.Char('Auth URL')
    app_key = fields.Char('App Key')
    app_secret = fields.Char('App Secret')
    access_token = fields.Char('Access Token')
    expire_access_token = fields.Float('Expire Access Token')
    refresh_token = fields.Char('Refresh Token')
    expire_refresh_token = fields.Float('Expire Refresh Token')
    tiktok_config_line_ids = fields.One2many('tiktok.config.line', 'tiktok_config_id', 'tiktok Config Line')
    active = fields.Boolean('Active', default=True)
    company_id = fields.Many2one('res.company', 'Company', required=True, default=lambda self: self.env.company)
    sign = fields.Char('Sign')
    start_date = fields.Date('Start Date', required=True, default=fields.Date.today())
    end_date = fields.Date('End Date')
    utm_medium_id = fields.Many2one('utm.medium', string="UTM Medium", default=lambda self: self.env['utm.medium'].search([('name', '=', 'TIKTOK')], limit=1))
    get_token_at = fields.Datetime('Get Token At')

    #-------------AUTH-------------------
    def action_refresh_token(self):
        tiktok_shops = self._get_ecommerce_shops()
        for tiktok_shop in tiktok_shops:
            tiktokApi = TiktokApiImpl(self)
            new_refresh_token = tiktokApi.get_refresh_token(tiktok_shop.refresh_token)
            if new_refresh_token:
                tiktok_shop.update_tiktok_tokens(new_refresh_token)
            self.env.cr.commit()

    #-------------MASTER DATA------------
    def action_fetch_shops(self):
        tiktok_shops = self._get_ecommerce_shops()
        for tiktok_shop in tiktok_shops:
            tiktokApi = TiktokApiImpl(self)
            shops = tiktokApi.fetch_shops(tiktok_shop.access_token)
            for shop in shops:
                shop['tiktok_config_id'] = self.id
                shop['utm_medium_id'] = self.utm_medium_id.id
                tiktok_shop.update_shop_info(shop)
                self.env.cr.commit()

    def action_fetch_warehouse(self):
        tiktok_shops = self._get_ecommerce_shops()
        for tiktok_shop in tiktok_shops:
            tiktokApi = TiktokApiImpl(self)
            warehouses = tiktokApi.fetch_warehouses(tiktok_shop.access_token)
            for warehouse in warehouses:
                warehouse['tiktok_config_id'] = self.id
                warehouse['utm_medium_id'] = self.utm_medium_id.id
                warehouse['ecommerce_shop_id'] = tiktok_shop.id
                self.env['ecommerce.warehouse.mapping'].init_from_tiktok(warehouse)
                self.env.cr.commit()

    def action_fetch_products(self, time_from_str:str=None, time_to_str:str=None, status:str='ALL', time_field_range = 'created_date'):
        # search_status = search_status or self._context.get('search_status')
        status = (status or self._context.get('filter', 'ALL')).upper()
        tiktok_shops = self._get_ecommerce_shops()
        tiktokApi = TiktokApiImpl(self)
        tiktok_products = {}
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        time_from = convert_str_date_to_timestamp(time_from_str)
        time_to = convert_str_date_to_timestamp(time_to_str)
        for tiktok_shop in tiktok_shops:
            products = tiktokApi.fetch_products(access_token=tiktok_shop.access_token, shop_cipher=tiktok_shop.shop_cipher, time_from=time_from, time_to=time_to, status=status, time_field_range=time_field_range)
            for product in products:
                product_details = tiktokApi.fetch_product_detail_v2(product['id'], tiktok_shop.access_token, tiktok_shop.shop_cipher)
                if product_details:
                    product_details['tiktok_config_id'] = self.id
                    product_details['utm_medium_id'] = self.utm_medium_id.id
                    product_details['ecommerce_shop_id'] = tiktok_shop.id
                    self.env['ecommerce.product.product'].init_from_tiktok(product_details)
                    self.env.cr.commit()
            tiktok_products.update({tiktok_shop.name: len(products)})
        return tiktok_products

    def action_fetch_product_details(self, shop_id=None, product_id=None):
        product_id = product_id or self._context.get('product_id')
        shop_id = shop_id or self._context.get('shop_id')
        tiktok_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id),('tiktok_config_id','=',self.id)], limit=1)
        if tiktok_shop:
            tiktokApi = TiktokApiImpl(self)
            product_details = tiktokApi.fetch_product_detail_v2(product_id, tiktok_shop.access_token, tiktok_shop.shop_cipher)
            if product_details:
                product_details['tiktok_config_id'] = self.id
                product_details['utm_medium_id'] = self.utm_medium_id.id
                product_details['ecommerce_shop_id'] = tiktok_shop.id
                self.env['ecommerce.product.product'].init_from_tiktok(product_details)
                self.env.cr.commit()

    def _get_ecommerce_shops(self):
        return self.env['ecommerce.shop'].search([('utm_medium_id', '=', self.utm_medium_id.id), ('tiktok_config_id', '=', self.id)])

    #-------------ORDER-----------------
    def action_fetch_orders(self, time_from_str:str=None, time_to_str:str=None,  order_status = None, time_field_range = 'created_date'):
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        time_from = convert_str_date_to_timestamp(time_from_str)
        time_to = convert_str_date_to_timestamp(time_to_str)
        order_status = order_status or self._context.get('order_status')
        time_field_range = time_field_range or self._context.get('time_field_range')
        tiktok_shops = self._get_ecommerce_shops()
        tiktokApi = TiktokApiImpl(self)
        tiktok_orders = []
        ecommerce_orders = []
        for tiktok_shop in tiktok_shops:
            orders = tiktokApi.fetch_orders(access_token=tiktok_shop.access_token, shop_cipher=tiktok_shop.shop_cipher, time_from=time_from, time_to=time_to, order_status=order_status, time_field_range=time_field_range)
            if len(orders) > 0:
                for order in orders:
                    order['tiktok_config_id'] = self.id
                    order['utm_medium_id'] = self.utm_medium_id.id
                    order['ecommerce_shop_id'] = tiktok_shop.id
                    try:
                        with self.env.cr.savepoint(flush=True):
                            ecommerce_order = self.env['ecommerce.order'].init_from_tiktok_json(order, self.utm_medium_id.id)
                            ecommerce_orders.append(ecommerce_order)
                    except Exception:
                        continue
            tiktok_orders.append({
                'utm_medium': self.utm_medium_id.name,
                'utm_medium_id': self.utm_medium_id.id,
                'ecommerce_shop_id': tiktok_shop.id,
                'ecommerce_shop_name': tiktok_shop.name,
                'total': len(orders),
            })
        if ecommerce_orders:
            ecommerce_order_lines = []
            for ec_order in ecommerce_orders:
                ecommerce_order_lines.extend(ec_order.order_line_ids)
            ec_product_not_instance = [
                l for l in ecommerce_order_lines
                if not l.has_ec_product
            ]
            if ec_product_not_instance:
                for item in ec_product_not_instance:
                    self.action_fetch_product_details(product_id=item.item_id, shop_id=item.ecommerce_order_id.ecommerce_shop_id.ec_shop_id)
        return tiktok_orders

    def action_fetch_order_details(self, order_ids=None):
        order_ids = order_ids or [self._context.get('order_id')]
        for shop_id, orders in order_ids.items():
            if not orders:
                continue
            shop_id = int(shop_id)
            tiktok_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id),('tiktok_config_id','=',self.id)], limit=1)
            if tiktok_shop:
                tiktokApi = TiktokApiImpl(self)
                order_details = tiktokApi.fetch_order_details(access_token=tiktok_shop.access_token, shop_cipher=tiktok_shop.shop_cipher, order_ids=order_ids)
                for order_detail in order_details:
                    order_detail['tiktok_config_id'] = self.id
                    order_detail['utm_medium_id'] = self.utm_medium_id.id
                    order_detail['ecommerce_shop_id'] = tiktok_shop.id
                    self.env['ecommerce.order'].init_from_tiktok_json(order_detail, self.utm_medium_id.id)
                    self.env.cr.commit()
            else:
                return {
                    "type": "ir.actions.client",
                    "tag": "display_notification",
                    "params": {
                        "title": "Notification",
                        "message": f'Not found shop {shop_id}',
                        "type": "danger",  # success | danger | warning | info
                        "sticky": False,
                    },
                }

    def action_fetch_return_orders(self, time_from_str:str=None, time_to_str:str=None,  order_status = None):
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        time_from = convert_str_date_to_timestamp(time_from_str)
        time_to = convert_str_date_to_timestamp(time_to_str)
        order_status = order_status or self._context.get('order_status')
        tiktok_shops = self._get_ecommerce_shops()
        tiktokApi = TiktokApiImpl(self)
        tiktok_orders = []
        for tiktok_shop in tiktok_shops:
            orders = tiktokApi.fetch_order_returns(access_token=tiktok_shop.access_token, shop_cipher=tiktok_shop.shop_cipher, time_from=time_from, time_to=time_to, order_status=order_status)
            if len(orders) > 0:
                for order in orders:
                    order['utm_medium_id'] = self.utm_medium_id.id
                    order['ecommerce_shop_id'] = tiktok_shop.id
                    order['order_type'] = 'return'
                    self.env['ecommerce.order'].init_return_order_from_tiktok_json(order, self.utm_medium_id.id)
                    self.env.cr.commit()
        return tiktok_orders

    def action_fetch_order_with_queue(self, time_from_str=None, time_to_str=None, order_status=None, time_field_range = 'created_date'):
        return self.with_delay().action_fetch_orders(time_from_str=time_from_str, time_to_str=time_to_str, order_status=order_status, time_field_range=time_field_range)

    def action_fetch_return_order_with_queue(self, time_from_str=None, time_to_str=None, order_status=None):
        return self.with_delay().action_fetch_return_orders(time_from_str=time_from_str, time_to_str=time_to_str, order_status=order_status)

    def fetch_product_with_queue(self, status:str='all'):
        return self.with_delay().action_fetch_products(status=status)
