# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
# from ..utils.tiki_api import TikiAPI
from odoo.addons.namtn_tiki.utils.tiki_api import TikiAPI
from datetime import datetime
from odoo.addons.queue_job.job import Job

_logger = logging.getLogger(__name__)

class TikiConfig(models.Model):
    _name = 'tiki.config'
    _description = 'TikiConfig'

    name = fields.Char('Name', required=True)
    base_url = fields.Char('Base Url', required=True)
    app_id = fields.Char('App Id', required=True)
    secret_key = fields.Char('Secret Key', required=True)
    grant_type = fields.Char('Grant Type', required=True)
    active = fields.Boolean('Active', default=True)
    start_date = fields.Datetime('Start Date', default=fields.Datetime.now())
    end_date = fields.Datetime('End Date')
    access_token = fields.Char('Access Token')
    expire_in = fields.Float('Expire In')
    tiki_config_line_ids = fields.One2many('tiki.config.line', 'tiki_config_id', 'Tiki Config Line')
    company_id = fields.Many2one('res.company', 'Company', required=True, default=lambda self: self.env.company)
    utm_medium_id = fields.Many2one('utm.medium', string="UTM Medium", required=True, default=lambda self: self.env['utm.medium'].search([('name', '=', 'TIKI')], limit=1), readonly=True)


    #-------------AUTH-------------------
    def action_get_access_token(self):
        tiki_shops = self._get_ecommerce_shops()
        for tiki_shop in tiki_shops:
            api = TikiAPI(tiki_shop.tiki_config_id)
            res = api.get_access_token()
            if res:
                tiki_shop.write({
                    'access_token': res['access_token'],
                    'expires_in': res['expires_in'],
                    'get_token_at': fields.Datetime.now(),
                })

    def _get_ecommerce_shops(self):
        return self.env['ecommerce.shop'].search([('utm_medium_id', '=', self.utm_medium_id.id), ('tiki_config_id', '=', self.id)])

    #-------------MASTER DATA------------
    def action_fetch_shop(self):
        tiki_shops = self._get_ecommerce_shops()
        for tiki_shop in tiki_shops:
            api = TikiAPI(tiki_shop.tiki_config_id)
            shop_info = api.fetch_shop(access_token=tiki_shop.access_token)
            if not shop_info:
                continue
            shop_info['tiki_config_id'] = self.id
            shop_info['shop_id'] = tiki_shop.ec_shop_id
            shop_info['utm_medium_id'] = self.utm_medium_id.id
            tiki_shop.write({
                'name': shop_info.get('name'),
                'ec_shop_id': str(shop_info.get('id')),
                'active': shop_info.get('active'),
            })
            self.env.cr.commit()

    def action_fetch_warehouse(self):
        tiki_shops = self._get_ecommerce_shops()
        for tiki_shop in tiki_shops:
            api = TikiAPI(tiki_shop.tiki_config_id)
            warehouses = api.fetch_warehouse(access_token=tiki_shop.access_token)
            for warehouse in warehouses:
                warehouse['tiki_config_id'] = self.id
                warehouse['utm_medium_id'] = self.utm_medium_id.id
                warehouse['ecommerce_shop_id'] = tiki_shop.id
                self.env['ecommerce.warehouse.mapping'].init_from_tiki(warehouse)
                self.env.cr.commit()

    def action_fetch_products(self):
        tiki_shops = self._get_ecommerce_shops()
        for tiki_shop in tiki_shops:
            api = TikiAPI(tiki_shop.tiki_config_id)
            products = api.fetch_products(access_token=tiki_shop.access_token)
            for product in products:
                product['tiki_config_id'] = self.id
                product['utm_medium_id'] = self.utm_medium_id.id
                product['ecommerce_shop_id'] = tiki_shop.id
                self.env['ecommerce.product.product'].init_from_tiki(product)
                self.env.cr.commit()

    #-------------ORDER-----------------
    def action_fetch_orders(self, time_from_str:str=None, time_to_str:str=None, time_field_range = 'created_date'):
        time_from_str = time_from_str or self._context.get('time_from_str', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_field_range = time_field_range or self._context.get('time_field_range', 'created_date')
        # time_to_str = time_to_str or self._context.get('time_to_str', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        tiki_shops = self._get_ecommerce_shops()
        for tiki_shop in tiki_shops:
            api = TikiAPI(tiki_shop.tiki_config_id)
            orders = api.fetch_orders( access_token=tiki_shop.access_token,page=1, limit=25, updated_from_date=time_from_str, updated_to_date=time_to_str, time_field_range=time_field_range)
            for order in orders:
                order['tiki_config_id'] = self.id
                order['utm_medium_id'] = self.utm_medium_id.id
                order['ecommerce_shop_id'] = tiki_shop.id
                try:
                    with self.env.cr.savepoint(flush=True):
                        self.env['ecommerce.order'].init_from_tiki_json(order, self.utm_medium_id.id)
                except Exception:
                    continue

    def action_fetch_order_with_queue(self, time_from_str=None, time_to_str=None, time_field_range = 'created_date'):
        return self.with_delay().action_fetch_orders(time_from_str=time_from_str, time_to_str=time_to_str, time_field_range=time_field_range)
