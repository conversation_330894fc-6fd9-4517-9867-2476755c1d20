# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.addons.namtn_lazada.utils.lazada_api import LazadaApi
from odoo.addons.namtn_lazada.tools.lazada_tools import five_minutes_between, check_expire_token, convert_str_date_to_iso,convert_str_date_to_timestamp
import json
from datetime import datetime

_logger = logging.getLogger(__name__)


class LazadaConfig(models.Model):
    _name = 'lazada.config'
    _description = 'LazadaConfig'

    name = fields.Char('Name')
    base_url = fields.Char('Base URL')
    auth_url = fields.Char('Auth URL')
    app_key = fields.Char('App Key')
    app_secret = fields.Char('App Secret')
    lazada_config_line_ids = fields.One2many('lazada.config.line', 'lazada_config_id', 'Lazada Config Line')
    code = fields.Char('Code')
    sign_method = fields.Char('Sign Method')
    active = fields.Boolean('Active', default=True)
    start_date = fields.Date('Start Date', required=True, default=fields.Date.today())
    end_date = fields.Date('End Date')
    access_token = fields.Char('Access Token')
    refresh_token = fields.Char('Refresh Token')
    expires_in = fields.Float('Expires In')
    refresh_expires_in = fields.Float('Refresh Expires In')
    get_token_at = fields.Datetime('Get Token At')
    company_id = fields.Many2one('res.company', 'Company', required=True, index=True, default=lambda self: self.env.company)
    utm_medium_id = fields.Many2one('utm.medium', 'UTM Medium', default=lambda self: self.env['utm.medium'].search([('name','=','LAZADA')], limit=1))

    #-------------AUTH-------------------
    def action_get_access_tokens(self):
        lazada_configs = self.search([])
        for lazada_config in lazada_configs:
            self._get_access_token(lazada_config)

    def action_get_access_token(self):
        self.ensure_one()
        self._get_access_token(lazada_config=self)

    def _get_access_token(self, lazada_config):
        lazada_api = LazadaApi(lazada_config)
        response = lazada_api.get_access_token()
        if response and response.body and response.body.get('code', '10') == '0':
            self.write({
                'access_token': response.body.get('access_token'),
                'refresh_token': response.body.get('refresh_token'),
                'refresh_expires_in': response.body.get('refresh_expires_in'),
                'expires_in': response.body.get('expires_in'),
                'get_token_at': fields.Datetime.now(),
            })
        return response

    def action_refresh_tokens(self):
        lazada_shops = self._get_ecommerce_shops()
        for lazada_shop in lazada_shops:
            self._refresh_token(lazada_shop)

    def action_refresh_token(self, refresh_token=None):
        self.ensure_one()
        refresh_token = refresh_token or self._context.get('refresh_token')
        self._refresh_token(lazada_config=self, refresh_token=refresh_token)

    def _refresh_token(self, lazada_shop):
        lazada_api = LazadaApi(self)
        response = lazada_api.get_refresh_token(lazada_shop.refresh_token)
        if response and response.body and response.body.get('code', '10') == '0':
            lazada_shop.update_lazada_tokens(response.body)
        return response

    #-------------MASTER DATA------------
    def action_fetch_products(self, time_from_str=None, time_to_str=None, filter:str='all'):
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        filter = filter or self._context.get('filter', 'all')
        lazada_shops = self._get_ecommerce_shops()
        lazada_api = LazadaApi(self)
        lazada_products = {}
        for lazada_shop in lazada_shops:
            time_from = convert_str_date_to_iso(time_from_str)
            time_to = convert_str_date_to_iso(time_to_str) if time_to_str else None
            products = lazada_api.fetch_products(time_from=time_from, time_to=time_to, access_token=lazada_shop.access_token, filter=filter)
            for lazada_product in products:
                lazada_product['utm_medium_id'] = self.utm_medium_id.id
                lazada_product['lazada_config_id'] = self.id
                lazada_product['ecommerce_shop_id'] = lazada_shop.id
                self.env['ecommerce.product.product'].init_from_lazada(json.loads(json.dumps(lazada_product)))
                self.env.cr.commit()
            lazada_products.update({lazada_shop.name: len(products)})
        return lazada_products

    def action_fetch_warehouse(self):
        lazada_shops = self._get_ecommerce_shops()
        for lazada_shop in lazada_shops:
            lazada_api = LazadaApi(self)
            warehouses = lazada_api.fetch_warehouse(lazada_shop.access_token)
            for warehouse in warehouses:
                warehouse['utm_medium_id'] = self.utm_medium_id.id
                warehouse['lazada_config_id'] = self.id
                warehouse['ecommerce_shop_id'] = lazada_shop.id
                warehouse['lazada_shop_name'] = lazada_shop.name
                self.env['ecommerce.warehouse.mapping'].init_from_lazada(json.loads(json.dumps(warehouse)))
                self.env.cr.commit()

    def action_fetch_shop_info(self):
        lazada_shops = self._get_ecommerce_shops()
        for lazada_shop in lazada_shops:
            lazada_api = LazadaApi(self)
            shop_info = lazada_api.fetch_shop_info(lazada_shop.access_token)
            if not shop_info:
                continue
            shop_info['lazada_config_id'] = self.id
            shop_info['shop_id'] = lazada_shop.ec_shop_id
            shop_info['utm_medium_id'] = self.utm_medium_id.id
            lazada_shop.init_from_lazada(shop_info)
            self.env.cr.commit()

    def action_fetch_product_detail(self, shop_id:int=None, product_id:int=None):
        shop_id = shop_id or self._context.get('shop_id')
        product_id = product_id or self._context.get('product_id')
        lazada_api = LazadaApi(self)
        lazada_shop = self.env['ecommerce.shop'].search([('id','=',shop_id)], limit=1)
        if lazada_shop:
            product_detail = lazada_api.fetch_product_detail(lazada_shop.access_token, product_id)
            if product_detail:
                product_detail['utm_medium_id'] = self.utm_medium_id.id
                product_detail['lazada_config_id'] = self.id
                product_detail['ecommerce_shop_id'] = lazada_shop.id
                self.env['ecommerce.product.product'].init_from_lazada(json.loads(json.dumps(product_detail)))


    #-------------ORDER-----------------
    def action_fetch_orders(self, time_from_str:str=None, time_to_str:str=None, status:str=None, time_field_range = 'created_date'):
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to')
        status = status or self._context.get('status')
        time_field_range = time_field_range or self._context.get('time_field_range')
        lazada_api = LazadaApi(self)
        # api_date = five_minutes_between()
        lazada_shops = self._get_ecommerce_shops()
        time_from = convert_str_date_to_iso(time_from_str)
        time_to = convert_str_date_to_iso(time_to_str) if time_to_str else None
        lazada_orders = []
        ecommerce_orders = []
        for lazada_shop in lazada_shops:
            orders = []
            orders = lazada_api.fetch_orders(access_token=lazada_shop.access_token, time_from_str=time_from, time_to_str=time_to, status=status, time_field_range=time_field_range)
            order_ids = [str(order.get('order_id')) for order in orders]
            lazada_order_lines = lazada_api.fetch_order_items(lazada_shop.access_token, order_ids)
            for lazada_order in orders:
                lazada_order['order_lines'] = [line for line in lazada_order_lines if line.get('order_id') == lazada_order.get('order_id')]
                lazada_order['utm_medium_id'] = self.utm_medium_id.id
                lazada_order['ecommerce_shop_id'] = lazada_shop.id
                try:
                    with self.env.cr.savepoint(flush=True):
                        ecommerce_order = self.env['ecommerce.order'].init_from_lazada_json(lazada_order, self.utm_medium_id.id)
                        ecommerce_orders.append(ecommerce_order)
                except Exception:
                    continue
            lazada_orders.append({
                'utm_medium': self.utm_medium_id.name,
                'utm_medium_id': self.utm_medium_id.id,
                'ecommerce_shop_id': lazada_shop.id,
                'ecommerce_shop_name': lazada_shop.name,
                'total': len(orders),
            })
        if ecommerce_orders:
            ecommerce_order_lines = []
            for ec_order in ecommerce_orders:
                ecommerce_order_lines.extend(ec_order.order_line_ids)
            ec_product_not_instance = [
                l for l in ecommerce_order_lines
                if not l.has_ec_product
            ]
            if ec_product_not_instance:
                for item in ec_product_not_instance:
                    self.action_fetch_product_detail(shop_id=item.ecommerce_order_id.ecommerce_shop_id.id, product_id=item.item_id.split('-')[0])
        return lazada_orders

    def action_fetch_order_detail(self, order_id:int=None, shop_id:str=None):
        order_id = order_id or self._context.get('order_id')
        for shop_id, orders in order_id.items():
            if not orders:
                continue
            shop_id = int(shop_id)
            lazada_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id)], limit=1)
            lazada_api = LazadaApi(self)
            lazada_order = lazada_api.fetch_order(access_token=lazada_shop.access_token, order_id=order_id)
            if lazada_order:
                lazada_order['order_lines'] = lazada_api.fetch_order_items(access_token=lazada_shop.access_token, order_ids=[str(order_id)])
                # lazada_order['order_lines'] = lazada_order['order_lines']
                lazada_order['ecommerce_shop_id'] = lazada_shop.id
                lazada_order['utm_medium_id'] = self.utm_medium_id.id
                lazada_order['order_type'] = 'normal'
                self.env['ecommerce.order'].init_from_lazada_json(lazada_order, self.utm_medium_id.id)
                self.env.cr.commit()

    def _get_ecommerce_shops(self):
        return self.env['ecommerce.shop'].search([('utm_medium_id', '=', self.utm_medium_id.id), ('lazada_config_id', '=', self.id)])

    def action_fetch_order_return(self, time_from_str:str=None, time_to_str:str=None):
        lazada_shops = self._get_ecommerce_shops()
        lazada_api = LazadaApi(self)
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        time_from = convert_str_date_to_timestamp(time_from_str)
        time_to = convert_str_date_to_timestamp(time_to_str) if time_to_str else None
        for lazada_shop in lazada_shops:
            lazada_returns = lazada_api.fetch_order_return(access_token=lazada_shop.access_token, time_from=time_from, time_to=time_to)
            for lazada_return in lazada_returns:
                lazada_return['utm_medium_id'] = self.utm_medium_id.id
                lazada_return['ecommerce_shop_id'] = lazada_shop.id
                lazada_return['order_type'] = 'return'
                self.env['ecommerce.order'].init_return_order_from_lazada_json(lazada_return, self.utm_medium_id.id)
                self.env.cr.commit()

    def action_fetch_order_with_queue(self, time_from_str:str=None, time_to_str:str=None, status:str=None, time_field_range:str = 'created_date'):
        return self.with_delay().action_fetch_orders(time_from_str=time_from_str, time_to_str=time_to_str, status=status, time_field_range=time_field_range)

    def action_fetch_order_return_with_queue(self, time_from_str:str=None, time_to_str:str=None):
        return self.with_delay().action_fetch_order_return(time_from_str=time_from_str, time_to_str=time_to_str)

    def fetch_product_with_queue(self, time_from_str:str=None, time_to_str:str=None, filter:str='all'):
        return self.with_delay().action_fetch_products(time_from_str=time_from_str, time_to_str=time_to_str, filter=filter)
