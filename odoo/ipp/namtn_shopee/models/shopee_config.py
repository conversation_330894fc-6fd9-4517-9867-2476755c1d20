# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import time
import requests
from datetime import timedelta, datetime
from ..utils.shopee_api_impl import ShopeeApiImpl
from ..tools.shopee_tools import five_minutes_between, convert_str_date_to_timestamp, check_expire_token
from collections import defaultdict

_logger = logging.getLogger(__name__)


class ShopeeConfig(models.Model):
    _name = 'shopee.config'
    _description = 'Shopee Config'
    _sql_constraints = [
        ('code_uniq', 'unique (code)', 'Shopee Config code must be unique!'),
    ]

    name = fields.Char('Name', required=True)
    # code = fields.Char('Code', required=True)
    base_url = fields.Char('Base URL', required=True)
    partner_id = fields.Integer('Partner ID')
    merchant_id = fields.Integer('Merchant ID')
    main_account_id = fields.Char('Main Account ID')
    timestamp = fields.Float('Timestamp')
    expire_in = fields.Float('Expire In')
    get_token_at = fields.Datetime('Get Token At')
    access_token = fields.Char('Access Token')
    refresh_token = fields.Char('Refresh Token')
    sign = fields.Char('Sign')
    shopee_authorize_signature = fields.Char('Shopee Authorize Signature')
    active = fields.Boolean('Active', default=True)
    start_date = fields.Date('Start Date', required=True, default=fields.Date.today())
    end_date = fields.Date('End Date')
    shopee_config_line_ids = fields.One2many('shopee.config.line', 'shopee_config_id', 'Shopee Config Line')
    utm_medium_id = fields.Many2one('utm.medium', string="UTM Medium", default=lambda self: self.env['utm.medium'].search([('name', '=', 'SHOPEE')], limit=1))
    company_id = fields.Many2one('res.company', 'Company', required=True, default=lambda self: self.env.company)

    def _get_ecommerce_shops(self):
        return self.env['ecommerce.shop'].search([('utm_medium_id', '=', self.utm_medium_id.id), ('shopee_config_id', '=', self.id)])

    #-------------AUTH-------------------
    def action_get_refresh_token(self):
        shopee_api = ShopeeApiImpl(conf=self)
        shops = self._get_ecommerce_shops()
        for shopee_shop in shops:
            refresh_token = shopee_api.get_refresh_token(shopee_shop.ec_shop_id, shopee_shop.refresh_token)
            if refresh_token:
                refresh_token['utm_medium_id'] = self.utm_medium_id.id
                shopee_shop.update_tokens(refresh_token)

    #-------------MASTER DATA------------
    def action_fetch_shops_by_partner(self):
        shopee_api = ShopeeApiImpl(conf=self)
        shopee_ids = shopee_api.fetch_shop_ids_by_partner()
        for shop in shopee_ids:
            shop['shopee_config_id'] = self.id
            shop['utm_medium_id'] = self.utm_medium_id.id
            self.env['ecommerce.shop'].init_from_shopee(shop)
            self.env.cr.commit()

    def action_fetch_shop_info(self):
        shopee_api = ShopeeApiImpl(conf=self)
        shops = self._get_ecommerce_shops()
        for shop in shops:
            shop_info = shopee_api.fetch_shop_info(shop.ec_shop_id, shop.access_token)
            shop_info['shopee_config_id'] = self.id
            shop_info['shop_id'] = shop.ec_shop_id
            shop_info['utm_medium_id'] = self.utm_medium_id.id
            self.env['ecommerce.shop'].init_from_shopee(shop_info)
            self.env.cr.commit()

    def action_fetch_merchant_by_partner(self):
        pass

    def action_fetch_warehouse(self):
        shopee_api = ShopeeApiImpl(conf=self)
        shop_ids = self._get_ecommerce_shops()
        for shop in shop_ids:
            shop_id = shop.ec_shop_id
            access_token = shop.access_token
            # self._check_expire_token(self, shop_id)
            warehouses = shopee_api.fetch_warehouse_detail(shop_id, access_token)
            for warehouse in warehouses:
                warehouse['utm_medium_id'] = self.utm_medium_id.id
                warehouse['shopee_config_id'] = self.id
                warehouse['shop_name'] = shop.name
                warehouse['ecommerce_shop_id'] = shop.id
                self.env['ecommerce.warehouse.mapping'].init_shopee_shop(warehouse)
                self.env.cr.commit()

    def action_fetch_item(self, time_from_str:str=None, time_to_str:str=None):
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        shopee_api = ShopeeApiImpl(conf=self)
        shop_ids = self._get_ecommerce_shops()
        time_from = convert_str_date_to_timestamp(time_from_str)
        time_to = convert_str_date_to_timestamp(time_to_str)
        shopee_products = {}
        for shop in shop_ids:
            shop_id = shop.ec_shop_id
            access_token = shop.access_token
            item_ids = shopee_api.fetch_item_list(shop_id=shop_id, access_token=access_token, time_from=time_from, time_to=time_to, limit=100)
            if len(item_ids) > 0:
                item_info_list = shopee_api.fetch_item_info(item_ids=item_ids, shop_id=shop_id, access_token=access_token)
                if len(item_info_list) > 0:
                    for item_info in item_info_list:
                        item_info['utm_medium_id'] = self.utm_medium_id.id
                        item_info['shopee_config_id'] = self.id
                        item_info['ecommerce_shop_id'] = shop.id
                        self.env['ecommerce.product.product'].init_from_shopee(item_info)
                        self.env.cr.commit()
            shopee_products.update({shop.name: len(item_ids)})
        return shopee_products

    def action_get_item_info(self, item_ids:list, shop_id:int):
        shopee_api = ShopeeApiImpl(conf=self)
        shop_id = shop_id or self._context.get('shop_id')
        shopee_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id),('shopee_config_id','=',self.id)], limit=1)
        item_info_list = shopee_api.fetch_item_info(item_ids, shop_id, shopee_shop.access_token)
        if len(item_info_list) > 0:
            for item_info in item_info_list:
                item_info['utm_medium_id'] = self.utm_medium_id.id
                item_info['shopee_config_id'] = self.id
                item_info['ecommerce_shop_id'] = shopee_shop.id
                self.env['ecommerce.product.product'].init_from_shopee(item_info)
                self.env.cr.commit()

    #-------------ORDER-----------------
    def action_fetch_tracking_number(self):
        shopee_api = ShopeeApiImpl(conf=self)
        shopee_shops = self._get_ecommerce_shops()
        for shopee_shop in shopee_shops:
            orders = self.env['ecommerce.order'].search([('ecommerce_shop_id','=',shopee_shop.id),('utm_medium_id','=',self.utm_medium_id.id), ('tracking_code','=',False), ('order_status', 'in', ['READY_TO_SHIP','PROCESSED', 'COMPLETED', 'SHIPPED', 'RETRY_SHIP']), ('order_type','=','normal')], order='create_date')
            for order in orders:
                tracking_number = shopee_api.fetch_tracking_number(order_sn=order.order_code, shop_id=shopee_shop.ec_shop_id, access_token=shopee_shop.access_token)
                if tracking_number:
                    order.write({
                        'tracking_code': tracking_number,
                    })
                    self.env.cr.commit()

    def action_fetch_tracking_number_detail(self, order_sn=None, shop_id=None):
        shopee_api = ShopeeApiImpl(conf=self)
        order_sn = self._context.get('order_sn') or order_sn
        shop_id = self._context.get('shop_id') or shop_id
        shopee_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id)], limit=1)
        tracking_number = shopee_api.fetch_tracking_number(order_sn=order_sn, shop_id=shop_id, access_token=shopee_shop.access_token)
        ecommerce_order =self.env['ecommerce.order'].search([('order_code','=',order_sn),('utm_medium_id','=',self.utm_medium_id.id), ('tracking_code','=',False)], limit=1)
        if ecommerce_order:
            ecommerce_order.write({
                'tracking_code': tracking_number,
            })
        return tracking_number

    def action_fetch_orders(self, time_from_str=None, time_to_str=None, time_range_field:str="create_time", order_status:str=None):
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        order_status = order_status or self._context.get('order_status')
        time_range_field = time_range_field or self._context.get('time_range_field')
        fast_delivery = self._get_fast_delivery_shipping()
        """
        time_to_str - time_from_str = 14days maximum
        """
        orders = []
        ecommerce_orders = []
        shopee_api = ShopeeApiImpl(conf=self)
        shopee_shops = self._get_ecommerce_shops()
        time_from = convert_str_date_to_timestamp(time_from_str)
        time_to = convert_str_date_to_timestamp(time_to_str) if time_to_str else None
        for shopee_shop in shopee_shops:
            # self._check_expire_token(conf, shop)
            ecommerce_shop_id = shopee_shop.id
            ec_shop_id = shopee_shop.ec_shop_id
            order_sns = shopee_api.fetch_orders(shop_id=int(ec_shop_id), access_token=shopee_shop.access_token, time_range_field=time_range_field, time_from=time_from, time_to=time_to, order_status=order_status)  # type: ignore
            if len(order_sns) > 0:
                order_details = shopee_api.fetch_order_details(order_sns=order_sns, shop_id=ec_shop_id, access_token=shopee_shop.access_token) # type: ignore
                if len(order_details) > 0:
                    for order_detail in order_details:
                        order_detail['item_list'] = order_detail.get('item_list', [])
                        order_detail['utm_medium_id'] = self.utm_medium_id.id
                        order_detail['ecommerce_shop_id'] = ecommerce_shop_id
                        shipping_carrier = order_detail.get('shipping_carrier')
                        if shipping_carrier in fast_delivery:
                            order_detail['checkout_shipping_carrier'] = 'fast'
                        try:
                            with self.env.cr.savepoint(flush=True):
                                ecommerce_order = self.env['ecommerce.order'].init_from_shopee_json(order_detail, self.utm_medium_id.id)
                                ecommerce_orders.append(ecommerce_order)
                        except Exception:
                            continue
            orders.append({
                'utm_medium': self.utm_medium_id.name,
                'utm_medium_id': self.utm_medium_id.id,
                'ecommerce_shop_id': shopee_shop.id,
                'ecommerce_shop_name': shopee_shop.name,
                'total': len(order_sns),
            })
        if ecommerce_orders:
            ecommerce_order_lines = []
            for ec_order in ecommerce_orders:
                ecommerce_order_lines.extend(ec_order.order_line_ids)
            ec_product_not_instance = [
                l for l in ecommerce_order_lines
                if not l.has_ec_product
            ]
            if ec_product_not_instance:
                unique_items = {(line.item_id, line.ecommerce_order_id.ecommerce_shop_id.ec_shop_id)
                                for line in ec_product_not_instance}
                item_ids_gr = defaultdict(list)
                for item_id, shop_id in unique_items:
                    item_ids_gr[shop_id].append(item_id)
                for shop_id, grouped_item_ids in item_ids_gr.items():
                    self.action_get_item_info(grouped_item_ids, int(shop_id))
        return orders

    def action_fetch_order_details(self):
        shopee_api = ShopeeApiImpl(conf=self)
        order_sn = self._context.get('order_sn')
        fast_delivery = self._get_fast_delivery_shipping()
        for shop_id, orders in order_sn.items():
            if not orders:
                continue
            shop_id = int(shop_id)
            shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id),('shopee_config_id','=',self.id)], limit=1)
            if not shop:
                return {
                    "type": "ir.actions.client",
                    "tag": "display_notification",
                    "params": {
                        "title": "Notification",
                        "message": f'Not found shop {shop_id}',
                        "type": "danger",  # success | danger | warning | info
                        "sticky": False,
                    },
                }
            order_details = shopee_api.fetch_order_details(order_sns=orders, shop_id=shop_id, access_token=shop.access_token)
            if len(order_details) > 0:
                for order_detail in order_details:
                    order_detail['shop_id'] = shop_id
                    order_detail['item_list'] = order_detail.get('item_list', [])
                    order_detail['ecommerce_shop_id'] = shop.id
                    shipping_carrier = order_detail.get('shipping_carrier')
                    if shipping_carrier in fast_delivery:
                        order_detail['checkout_shipping_carrier'] = 'fast'
                    self.env['ecommerce.order'].init_from_shopee_json(order_detail, self.utm_medium_id.id)
                    self.env.cr.commit()

    def _get_fast_delivery_shipping(self):
        fast_delivery = self.shopee_config_line_ids.search([('shopee_endpoint_key','=','shopee_config.fast_delivery')],limit=1).shopee_endpoint_url
        if fast_delivery:
            fast_delivery = fast_delivery.split(',')
        return fast_delivery or []

    def action_fetch_order_return(self, time_from_str=None, time_to_str=None):
        time_from_str = time_from_str or self._context.get('time_from', datetime.now().strftime('%Y-%m-%d 00:00:00'))
        time_to_str = time_to_str or self._context.get('time_to', datetime.now().strftime('%Y-%m-%d 23:59:59'))
        shopee_api = ShopeeApiImpl(conf=self)
        shop_ids = self._get_ecommerce_shops()
        time_from = convert_str_date_to_timestamp(time_from_str)
        time_to = convert_str_date_to_timestamp(time_to_str) if time_to_str else None
        returns = []
        for shop_id in shop_ids:
            return_list = shopee_api.fetch_order_return(shop_id=shop_id.ec_shop_id, access_token=shop_id.access_token, time_from=time_from, time_to=time_to)
            if len(return_list) > 0:
                for return_detail in return_list:
                    return_detail['order_type'] = 'return'
                    return_detail['ecommerce_shop_id'] = shop_id.id
                    return_detail['utm_medium_id'] = self.utm_medium_id.id
                    return_detail['item_list'] = return_detail.get('item', [])
                    self.env['ecommerce.order'].init_from_shopee_json(return_detail, self.utm_medium_id.id)
                    self.env.cr.commit()
            returns.append({
                'utm_medium': self.utm_medium_id.name,
                'utm_medium_id': self.utm_medium_id.id,
                'ecommerce_shop_id': shop_id.id,
                'ecommerce_shop_name': shop_id.name,
                'total': len(return_list),
            })
        return returns

    def action_fetch_booking_shipping_parameter(self, order_sn: list = [], shop_id: str = None):
        shopee_api = ShopeeApiImpl(conf=self)
        order_sn = order_sn or self._context.get('order_sn')
        shop_id = shop_id or self._context.get('shop_id')
        shopee_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id),('shopee_config_id','=',self.id)], limit=1)
        booking_shipping_parameter = shopee_api.fetch_booking_shipping_parameter(access_token=shopee_shop.access_token, order_sn=order_sn, shop_id=shop_id)
        return booking_shipping_parameter

    def action_ship_order(self, order_sn: str = None, shop_id: str = None, pickup: dict = {}):
        shopee_api = ShopeeApiImpl(conf=self)
        order_sn = order_sn or self._context.get('order_sn')
        shop_id = shop_id or self._context.get('shop_id')
        shopee_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id),('shopee_config_id','=',self.id)], limit=1)
        ship_order = shopee_api.ship_order(access_token=shopee_shop.access_token, shop_id=shop_id, order_sn=order_sn, pickup=pickup)
        return ship_order

    def action_get_shipping_document_parameter(self, order_sn: str = None, shop_id: str = None):
        shopee_api = ShopeeApiImpl(conf=self)
        order_sn = order_sn or self._context.get('order_sn')
        shop_id = shop_id or self._context.get('shop_id')
        shopee_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id),('shopee_config_id','=',self.id)], limit=1)
        shipping_document_parameter = shopee_api.fetch_shipping_document_parameter(access_token=shopee_shop.access_token, order_sn=order_sn, shop_id=shop_id)
        return shipping_document_parameter

    def action_create_shipping_document(self, order_sn: list = [], shop_id: str = None):
        shopee_api = ShopeeApiImpl(conf=self)
        order_sn = order_sn or self._context.get('order_sn')
        shop_id = shop_id or self._context.get('shop_id')
        shopee_shop = self.env['ecommerce.shop'].search([('ec_shop_id','=',shop_id),('shopee_config_id','=',self.id)], limit=1)
        create_shipping_document = shopee_api.create_shipping_document(access_token=shopee_shop.access_token, order_sn=order_sn, shop_id=shop_id)
        return create_shipping_document

    #-------------QUEUES-----------------
    def action_fetch_order_with_queue(self, time_from_str=None, time_to_str=None, time_range_field:str="create_time", order_status:str=None):
        return self.with_delay().action_fetch_orders(time_from_str=time_from_str, time_to_str=time_to_str, time_range_field=time_range_field, order_status=order_status)

    def action_fetch_tracking_number_with_queue(self):
        return self.with_delay().action_fetch_tracking_number()

    def action_fetch_order_return_with_queue(self, time_from_str=None, time_to_str=None):
        return self.with_delay().action_fetch_order_return(time_from_str=time_from_str, time_to_str=time_to_str)

    def fetch_product_with_queue(self, time_from_str:str=None, time_to_str:str=None):
        return self.with_delay().action_fetch_item(time_from_str=time_from_str, time_to_str=time_to_str)
